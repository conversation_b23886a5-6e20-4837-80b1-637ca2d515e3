# NextJS SEO Business Schema

To ensure good SEO, your Supabase business table should include the following fields:

| Field Name         | Type        | Description                                      |
|--------------------|-------------|--------------------------------------------------|
| id                 | UUID        | Unique identifier for each business              |
| name               | Text        | Business name                                    |
| slug               | Text        | URL-friendly unique identifier                   |
| address            | Text        | Full business address                            |
| city               | Text        | City                                             |
| region             | Text        | State/Region                                     |
| postal_code        | Text        | Postal/ZIP code                                  |
| phone              | Text        | Business phone number                            |
| email              | Text        | Contact email                                    |
| website_url        | Text        | Business website                                 |
| description        | Text        | Detailed business description                    |
| categories         | Text[]      | List of service categories/keywords              |
| opening_hours      | Text        | Business hours (e.g., "Mon-Fri 8am-6pm")         |
| latitude           | Float       | Google Maps latitude                             |
| longitude          | Float       | Google Maps longitude                            |
| logo_url           | Text        | Logo image URL                                   |
| images             | Text[]      | Array of image URLs                              |
| reviews            | JSONB       | Customer reviews and ratings                     |
| social_links       | JSONB       | Social media links (Facebook, Instagram, etc.)   |
| area_served        | Text[]      | Cities/regions served                            |
| tags               | Text[]      | Tags for internal linking                        |
| schema_org         | JSONB       | Schema.org structured data (optional)            |
| created_at         | Timestamp   | Record creation date                             |
| updated_at         | Timestamp   | Last update date                                 |

**Notes:**
- Use `slug` for clean URLs and dynamic routing.
- Store reviews and social links as JSON for flexibility.
- Include all NAP (Name, Address, Phone) fields for local SEO.
- Add schema_org field for custom structured data if needed.

This schema supports rich content, structured data, and internal linking for optimal SEO.
