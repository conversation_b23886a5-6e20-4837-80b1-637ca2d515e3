# NextJS SEO Mother Prompt - Directory Website Builder

## Overview
You are tasked with building a comprehensive, SEO-optimized Next.js directory website using Supabase as the backend. This directory must be designed for maximum Google search engine visibility and follow all modern SEO best practices. The instructions should work for any business niche - whether it's restaurants, plumbers, doctors, fitness centers, or any other local business category.

## Prerequisites
- Supabase project with business data following the provided schema
- Initial design concept/wireframes
- Target keywords and business categories
- Geographic focus areas

---

## Phase 1: Project Foundation

### 1.1 Initialize the Next.js Project
Create a new Next.js project with TypeScript, Tailwind CSS, and ESLint enabled. Install the necessary Supabase packages for database integration, along with SEO-focused packages for sitemap generation and performance analysis.

### 1.2 Configure Next.js for Maximum SEO Performance
Set up your Next.js configuration to optimize images by enabling modern formats like WebP and AVIF. Enable compression, disable unnecessary headers, and configure experimental CSS optimization. Point your image domains to your Supabase storage URL.

### 1.3 Establish Supabase Connection
Create a properly typed Supabase client that connects to your database schema. Ensure the connection supports both server-side and client-side operations for optimal performance.

---

## Phase 2: Database Schema Implementation

### 2.1 Core Business Data Structure
Your Supabase business table should include essential fields that support both functionality and SEO. Include basic business information like name, description, contact details, and location data. Create URL-friendly slugs for each business that can be used in clean URLs.

### 2.2 SEO-Enhanced Fields
Add specialized fields that boost SEO performance. Include arrays for business categories to enable service-based linking, areas served for local SEO expansion, and tags for content categorization. Store business hours for rich snippet compatibility, reviews data for processing, and structured data for search engine understanding.

### 2.3 Supporting SEO Tables
Create additional tables that enhance your SEO strategy. Build a categories table with SEO-optimized titles and descriptions, plus hierarchical support. Add a locations table with city and region data, including population and geographic coordinates. Consider a keywords table that tracks search volume and difficulty for your target terms.

---

## Phase 3: URL Structure and Page Architecture

### 3.1 SEO-Friendly Page Structure
Design your page architecture to support both users and search engines. Create a logical hierarchy starting with your homepage, flowing to category pages, then location-specific category pages, and finally individual business pages. Include dedicated location pages and search functionality.

### 3.2 Clean URL Patterns
Implement URLs that are both user-friendly and SEO-optimized. Use patterns that combine categories and locations naturally, like showing plumbers in a specific city, or restaurants in a neighborhood. Make individual business pages accessible through intuitive paths that include the business name.

---

## Phase 4: Search Engine Optimization Foundation

### 4.1 Dynamic Meta Tag Strategy
Create intelligent meta tags that adapt to your content. For your homepage, emphasize your city or region plus the fact that you're a comprehensive business directory. Category pages should highlight the best providers in specific locations with social proof indicators. Business pages should showcase the individual business with their services, location, ratings, and contact information.

### 4.2 Structured Data Implementation
Implement comprehensive schema markup that helps search engines understand your content. Add LocalBusiness schema for individual businesses including their address, contact information, coordinates, hours, and review aggregations. Include breadcrumb navigation schema to show page relationships clearly.

### 4.3 Semantic HTML Best Practices
Structure your HTML using proper semantic elements. Use header tags for site navigation, main tags for primary content, article tags for business listings, section tags for content categories, and aside tags for supplementary content like filters or related businesses.

---

## Phase 5: Review Content Strategy

### 5.1 Safe Review Display Approach
Display review information without copying original text to avoid copyright issues. Show aggregated data like total review counts and average star ratings prominently. Generate original sentiment summaries that capture the essence of customer feedback without quoting directly. Always include links directing users to read full reviews on the original platform.

### 5.2 Content Generation from Review Insights
Transform review data into valuable original content. Extract common service highlights mentioned by customers and rewrite them as business strengths. Identify frequently asked questions or concerns from reviews and create helpful FAQ sections. Generate blog topic ideas based on common themes in customer feedback.

### 5.3 Review Processing Workflow
Create functions that analyze review data to extract useful information. Calculate aggregate ratings and review counts, generate sentiment-based summaries, identify service keywords that customers mention frequently, and suggest FAQ content based on common customer questions or issues.

---

## Phase 6: Performance Optimization

### 6.1 Core Web Vitals Excellence
Optimize for Google's Core Web Vitals metrics. Use Next.js Image components with proper sizing and lazy loading for images below the fold. Minimize layout shifts by reserving space for dynamic content. Ensure your largest contentful paint happens quickly by prioritizing critical content loading.

### 6.2 Strategic Caching Implementation
Use Next.js static generation for pages that don't change frequently, like category and location pages. Implement Incremental Static Regeneration for business pages that need periodic updates. Set appropriate revalidation times based on how often your business data changes.

### 6.3 Bundle and Performance Optimization
Implement code splitting so users only download JavaScript they need for each page. Use dynamic imports for heavy components that aren't immediately necessary. Remove unused dependencies and optimize your bundle size through tree shaking.

---

## Phase 7: Local SEO Mastery

### 7.1 Location-Specific Pages
Create dedicated pages for each geographic area you serve. Build pages that combine services with locations naturally, ensuring each combination provides unique value. Make sure every service area has its own dedicated page with relevant local content.

### 7.2 Geographic Content Enhancement
Embed interactive maps for businesses when appropriate. Include references to local landmarks, neighborhoods, and geographic features that locals would recognize. Create content that demonstrates local knowledge and serves different areas comprehensively.

### 7.3 NAP Consistency Excellence
Ensure Name, Address, and Phone number information is identical across your entire site. This includes business listings, footer information, contact pages, and structured data. Consistency in NAP information is crucial for local SEO success and should extend to any external directory listings.

---

## Phase 8: Internal Linking Strategy

### 8.1 Hierarchical Link Structure
Create clear pathways for users and search engines to navigate your content. Link from your homepage to major category pages, from category pages to location-specific versions, and from there to individual businesses. Also create direct paths from location pages to relevant categories and businesses.

### 8.2 Contextual Content Linking
Within business descriptions and content, naturally link to related services and businesses. Cross-reference businesses that serve the same areas or offer complementary services. Create "Related Businesses" sections that provide value to users while distributing link equity throughout your site.

### 8.3 Strategic Anchor Text Usage
Use descriptive, keyword-rich anchor text that tells users and search engines what to expect on the linked page. Instead of generic "click here" links, use phrases that include location and service information, making the links both informative and SEO-friendly.

---

## Phase 9: Content Generation Excellence

### 9.1 Category Page Content Development
For each business category, create comprehensive content that serves users researching these services. Include overviews of what the service entails, guidance on selecting quality providers, typical pricing considerations, and answers to common questions. Add local market insights that demonstrate expertise in your area.

### 9.2 Location-Specific Content Creation
Develop unique content for each geographic area you cover. Include demographic information, popular services in that area, local business climate insights, and transportation or accessibility details. Connect each location to all relevant service categories available there.

### 9.3 Business Page Enhancement
Beyond basic business information, add value through service area descriptions, photo galleries showcasing the business, FAQ sections derived from review analysis, suggestions for related businesses, and comparative information that helps users make informed decisions.

---

## Phase 10: Advanced User Experience Features

### 10.1 Intelligent Search Functionality
Build search capabilities that include auto-complete suggestions, filtering options by category, location, and ratings. Ensure search result pages have proper meta tags and provide valuable user experiences. Track search analytics to understand user behavior and improve results.

### 10.2 Mobile-First Excellence
Design with mobile users as the primary consideration, since most local searches happen on mobile devices. Ensure touch-friendly navigation, fast loading speeds on mobile networks, and consider implementing AMP pages for critical content that needs to load extremely quickly.

### 10.3 Accessibility for SEO Benefits
Implement comprehensive accessibility features that also boost SEO. Add descriptive alt text for all images, maintain proper heading hierarchies from H1 to H6, ensure keyboard navigation works throughout the site, and maintain proper color contrast ratios for screen readers and visual accessibility.

---

## Phase 11: Technical SEO Foundation

### 11.1 Automatic Sitemap Generation
Configure your site to automatically generate XML sitemaps that include all your pages. Set up robots.txt files that guide search engine crawlers appropriately, allowing access to public content while restricting admin areas or API endpoints.

### 11.2 Canonical URL Implementation
Prevent duplicate content issues by implementing canonical tags throughout your site. This is especially important for directory sites where similar content might appear in multiple locations or be accessible through different URL patterns.

### 11.3 HTTPS and Security Configuration
Ensure your entire site operates over HTTPS, which is both a ranking factor and essential for user trust. Configure security headers and ensure all external links and resources also use secure protocols.

---

## Phase 12: Analytics and Performance Monitoring

### 12.1 Comprehensive Analytics Setup
Implement Google Analytics 4 to track organic search traffic, page performance, user engagement patterns, and conversion events. Set up goal tracking for business contact actions, directions requests, and other valuable user interactions.

### 12.2 Search Console Integration
Connect your site to Google Search Console to monitor search performance, indexing status, Core Web Vitals scores, and mobile usability. Use this data to identify optimization opportunities and track your SEO progress over time.

### 12.3 Performance Monitoring Tools
Implement both real user monitoring and synthetic monitoring to track site performance. Monitor SEO rankings for target keywords and track how your optimizations impact search visibility over time.

---

## Phase 13: Pre-Launch Quality Assurance

### 13.1 SEO Implementation Audit
Verify that all meta tags are properly implemented and unique for each page. Ensure structured data validates correctly using Google's testing tools. Confirm that internal linking strategies are working and that mobile responsiveness meets modern standards.

### 13.2 Content Quality Review
Review all business descriptions to ensure they're unique and helpful to users. Verify that review processing is working correctly and generating valuable content. Check that FAQ sections are populated and that local SEO elements are properly implemented.

### 13.3 Technical Performance Check
Test that sitemaps are generating correctly and being submitted to search engines. Verify robots.txt configuration, canonical URL implementation, and that HTTPS is properly configured throughout the site. Ensure 404 pages provide helpful user experiences.

---

## Phase 14: Growth and Optimization Strategy

### 14.1 Content Expansion Planning
Develop ongoing content strategies based on review insights and user behavior. Plan seasonal content that aligns with service demand patterns, integrate local events and news when relevant, and create opportunities for user-generated content that adds value.

### 14.2 Authority Building Preparation
Create valuable resources like guides, tools, or educational content that other sites will want to link to. Develop relationships with local businesses and industry organizations that can provide citation and linking opportunities. Build a foundation for ongoing citation building and local authority development.

### 14.3 Continuous Improvement Process
Establish processes for ongoing optimization including A/B testing of meta descriptions, enhancement of schema markup based on new opportunities, regular page speed optimization, and user experience improvements based on analytics data and user feedback.

---

## Success Measurement Framework

Track these essential metrics to measure your SEO success:
- Growth in organic search traffic from target geographic areas
- Improvement in local search rankings for category plus location terms
- Increased click-through rates from search results to your pages
- Higher engagement with business listings leading to contact actions
- Growth in the number and quality of business reviews
- Consistent improvement in Core Web Vitals scores
- Strong mobile search performance and user experience metrics

---

## Universal Implementation Principles

1. **Prioritize User Experience**: Google's algorithms increasingly favor sites that provide excellent user experiences, so every SEO decision should also benefit users.

2. **Focus on Local Relevance**: Directory sites succeed by demonstrating deep local knowledge and serving specific geographic communities effectively.

3. **Build Genuine Authority**: Create content and features that establish your site as the go-to resource for local business discovery in your market.

4. **Maintain Content Quality**: Every page should provide unique value that users can't easily find elsewhere, avoiding thin or duplicate content.

5. **Mobile-First Thinking**: Since most local searches happen on mobile devices, ensure every feature works excellently on smartphones and tablets.

6. **Schema Markup Excellence**: Structured data helps search engines understand and display your content effectively in search results.

7. **Strategic Internal Linking**: Create logical pathways that distribute authority throughout your site while helping users discover relevant content.

8. **Performance as a Priority**: Fast-loading pages are essential for both user experience and search engine rankings, especially on mobile devices.

This comprehensive approach ensures your directory website serves users effectively while achieving maximum visibility in search engines, regardless of the specific business niche you're targeting.
