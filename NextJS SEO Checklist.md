# Next.js SEO Checklist

## 1. Keyword Research
- [ ] Identify high-intent keywords (e.g., "emergency garage door repair near me")
- [ ] Use keywords naturally in page titles, headings, and content

## 2. Technical SEO
- [ ] Use semantic HTML tags (`<header>`, `<main>`, `<footer>`, `<article>`, `<section>`)
- [ ] Optimize page speed (Next.js Image component, caching, minimal scripts)
- [ ] Ensure mobile responsiveness
- [ ] Implement accessibility (alt text, ARIA, color contrast, keyboard navigation)

## 3. Meta Tags
- [ ] Set unique and descriptive `<title>` and `<meta description>` for each page
- [ ] Add Open Graph and Twitter meta tags for social sharing

## 4. Structured Data
- [ ] Add JSON-LD schema for local business, reviews, and services

## 5. Internal Linking
- [ ] Link related pages for better navigation and SEO

## 6. Content Quality
- [ ] Write clear, helpful, and original content for each service/location
- [ ] Include FAQs, testimonials, and trust signals

## 7. Local SEO
- [ ] Create dedicated pages for each service area
- [ ] Embed Google Maps and local contact info
- [ ] Ensure NAP (Name, Address, Phone) consistency

## 8. Performance
- [ ] Use Next.js SSR/SSG for fast initial loads
- [ ] Minimize third-party scripts

## 9. Sitemap & Robots.txt
- [ ] Generate and submit XML sitemap
- [ ] Use robots.txt to control crawling
