# SQL for {{placeholder}}-raw Table

```sql
CREATE TABLE "{{placeholder}}-raw" (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  raw_data jsonb NOT NULL,
  cleanup boolean DEFAULT false,
  created_at timestamptz DEFAULT now(),
  processed_at timestamptz
);
```


# SQL for {{placeholder}}-reviews-raw Table

```sql
CREATE TABLE "{{placeholder}}-reviews-raw" (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  review_id text,
  url text NOT NULL,
  author text,
  rating numeric,
  text text,
  created_at timestamptz DEFAULT now(),
  review_date timestamptz,
  raw_data jsonb
);
```

# Function: transfer_reviews_to_clean

```sql
CREATE OR REPLACE FUNCTION transfer_reviews_to_clean()
RETURNS TABLE(transferred_count integer, updated_count integer)
LANGUAGE plpgsql
AS $function$
DECLARE
    transferred_count INTEGER := 0;
    updated_count INTEGER := 0;
    review_record RECORD;
BEGIN
    -- Loop through unprocessed records in {{placeholder}}-reviews-raw
    FOR review_record IN 
        SELECT id, raw_data, url 
        FROM "{{placeholder}}-reviews-raw" 
        WHERE cleanup = false AND url IS NOT NULL
    LOOP
        -- Update {{placeholder}}-clean table with reviews data using URL matching
        UPDATE "{{placeholder}}-clean" 
        SET reviews_data = review_record.raw_data
        WHERE url = review_record.url;
        
        -- Check if update was successful
        IF FOUND THEN
            transferred_count := transferred_count + 1;
            
            -- Mark record as processed in {{placeholder}}-reviews-raw
            UPDATE "{{placeholder}}-reviews-raw"
            SET cleanup = true, processed_at = NOW()
            WHERE id = review_record.id;
            
            updated_count := updated_count + 1;
        END IF;
    END LOOP;
    
    -- Return counts
    RETURN QUERY SELECT transferred_count, updated_count;
END;
$function$
```

# Function: process_clean_data

```sql
CREATE OR REPLACE FUNCTION process_clean_data()
RETURNS TABLE(processed_count integer, error_count integer, message text)
LANGUAGE plpgsql
AS $function$
DECLARE
  rec RECORD;
  processed_cnt INTEGER := 0;
  error_cnt INTEGER := 0;
  price_numeric NUMERIC;
  parsed_json JSONB;
  temp_str TEXT;
BEGIN
  FOR rec IN SELECT * FROM "{{placeholder}}-raw" WHERE cleanup = FALSE
  LOOP
    BEGIN
      -- Extract the JSON string from JSONB and parse it
      temp_str := rec.raw_data #>> '{}';
      parsed_json := temp_str::jsonb;
      
      -- Handle price conversion
      IF parsed_json->>'price' IS NOT NULL AND parsed_json->>'price' ~ '^\\d+(\\.\\d+)?$' THEN
        price_numeric := (parsed_json->>'price')::NUMERIC;
      ELSE
        price_numeric := NULL;
      END IF;
      INSERT INTO "{{placeholder}}-clean" (
        source_id,
        title,
        price,
        category_name,
        address,
        neighborhood,
        street,
        city,
        postal_code,
        state,
        country_code,
        website,
        phone,
        phone_unformatted,
        claim_this_business,
        lat,
        lng,
        total_score,
        permanently_closed,
        temporarily_closed,
        place_id,
        categories,
        fid,
        cid,
        reviews_count,
        images_count,
        image_categories,
        scraped_at,
        google_food_url,
        hotel_ads,
        opening_hours,
        people_also_search,
        places_tags,
        reviews_tags,
        additional_info,
        gas_prices,
        search_page_url,
        search_string,
        language,
        rank,
        is_advertisement,
        image_url,
        kgmid,
        url
      ) VALUES (
        rec.id,
        COALESCE(parsed_json->>'title', ''),
        price_numeric,
        COALESCE(parsed_json->>'categoryName', ''),
        COALESCE(parsed_json->>'address', ''),
        COALESCE(parsed_json->>'neighborhood', ''),
        COALESCE(parsed_json->>'street', ''),
        COALESCE(parsed_json->>'city', ''),
        COALESCE(parsed_json->>'postalCode', ''),
        COALESCE(parsed_json->>'state', ''),
        COALESCE(parsed_json->>'countryCode', ''),
        COALESCE(parsed_json->>'website', ''),
        COALESCE(parsed_json->>'phone', ''),
        COALESCE(parsed_json->>'phoneUnformatted', ''),
        COALESCE((parsed_json->>'claimThisBusiness')::BOOLEAN, FALSE),
        COALESCE((parsed_json->'location'->>'lat')::DOUBLE PRECISION, NULL),
        COALESCE((parsed_json->'location'->>'lng')::DOUBLE PRECISION, NULL),
        COALESCE((parsed_json->>'totalScore')::DOUBLE PRECISION, NULL),
        COALESCE((parsed_json->>'permanentlyClosed')::BOOLEAN, FALSE),
        COALESCE((parsed_json->>'temporarilyClosed')::BOOLEAN, FALSE),
        COALESCE(parsed_json->>'placeId', ''),
        COALESCE(parsed_json->'categories', '[]'::jsonb),
        COALESCE(parsed_json->>'fid', ''),
        COALESCE(parsed_json->>'cid', ''),
        COALESCE((parsed_json->>'reviewsCount')::INTEGER, 0),
        COALESCE((parsed_json->>'imagesCount')::INTEGER, 0),
        COALESCE(parsed_json->'imageCategories', '[]'::jsonb),
        COALESCE((parsed_json->>'scrapedAt')::TIMESTAMP WITH TIME ZONE, NULL),
        COALESCE(parsed_json->>'googleFoodUrl', ''),
        COALESCE(parsed_json->'hotelAds', '[]'::jsonb),
        COALESCE(parsed_json->'openingHours', '[]'::jsonb),
        COALESCE(parsed_json->'peopleAlsoSearch', '[]'::jsonb),
        COALESCE(parsed_json->'placesTags', '[]'::jsonb),
        COALESCE(parsed_json->'reviewsTags', '[]'::jsonb),
        COALESCE(parsed_json->'additionalInfo', '{}'::jsonb),
        COALESCE(parsed_json->'gasPrices', '[]'::jsonb),
        COALESCE(parsed_json->>'searchPageUrl', ''),
        COALESCE(parsed_json->>'searchString', ''),
        COALESCE(parsed_json->>'language', ''),
        COALESCE((parsed_json->>'rank')::INTEGER, NULL),
        COALESCE((parsed_json->>'isAdvertisement')::BOOLEAN, FALSE),
        COALESCE(parsed_json->>'imageUrl', ''),
        COALESCE(parsed_json->>'kgmid', ''),
        COALESCE(parsed_json->>'url', '')
      );
      
      UPDATE "{{placeholder}}-raw" SET cleanup = TRUE, processed_at = NOW() WHERE id = rec.id;
      processed_cnt := processed_cnt + 1;
    EXCEPTION WHEN OTHERS THEN
      error_cnt := error_cnt + 1;
      RAISE WARNING 'Error processing record %: %', rec.id, SQLERRM;
    END;
  END LOOP;
  RETURN QUERY SELECT processed_cnt, error_cnt, FORMAT('Successfully processed %s records, %s errors', processed_cnt, error_cnt);
END;
$function$
```
