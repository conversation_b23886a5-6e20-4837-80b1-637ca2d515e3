// describe_instagram_image.js

// TODO: Replace with your actual OpenRouter API key
const OPENROUTER_API_KEY = "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";

// TODO: Replace with your actual site URL and site name if needed
const YOUR_SITE_URL = "<YOUR_SITE_URL>";
const YOUR_SITE_NAME = "<YOUR_SITE_NAME>";

// TODO: Replace with the Instagram image URL you want to describe
const INSTAGRAM_IMAGE_URL = "https://instagram.fsin3-1.fna.fbcdn.net/v/t51.29350-15/455112360_1598332987395520_3883180903226004116_n.jpg?stp=dst-jpg_e35_tt6&efg=eyJ2ZW5jb2RlX3RhZyI6IkNBUk9VU0VMX0lURU0uaW1hZ2VfdXJsZ2VuLjEwODB4MTA4MC5zZHIuZjI5MzUwLmRlZmF1bHRfaW1hZ2UuYzIifQ&_nc_ht=instagram.fsin3-1.fna.fbcdn.net&_nc_cat=104&_nc_oc=Q6cZ2QHANNVRE8FwjMjluR70g8r5akVTtOc2N5cQAyApyHLdHSqZFtUJFjfnK3VXpjd--Mc&_nc_ohc=g2LhgUy0cy0Q7kNvwHAuydc&_nc_gid=A_AxXmdHErCBkR08GHw8CQ&edm=APs17CUBAAAA&ccb=7-5&ig_cache_key=MzQzNDA4NDc5Mzk1NTU3OTMzMA%3D%3D.3-ccb7-5&oh=00_AfWwAcT9Qls3kJz33sTmmjXGnKC5dn515ROBpy5K71vp8Q&oe=68AE0CEF&_nc_sid=10d13b";

async function describeImage() {
  const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
    method: "POST",
    headers: {
      "Authorization": `Bearer ${OPENROUTER_API_KEY}`,
      "Content-Type": "application/json",
      "HTTP-Referer": YOUR_SITE_URL,
      "X-Title": YOUR_SITE_NAME
    },
    body: JSON.stringify({
      model: "google/gemini-2.5-flash-lite",
      messages: [
        {
          role: "user",
          content: [
            {
              type: "text",
              text: "describe this image?"
            },
            {
              type: "image_url",
              image_url: {
                url: INSTAGRAM_IMAGE_URL
              }
            }
          ]
        }
      ]
    })
  });

  const data = await response.json();
  console.log(data.choices?.[0]?.message?.content);
}

describeImage();
