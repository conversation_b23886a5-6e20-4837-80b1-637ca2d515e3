# Prompt: Build an SEO-Ready Next.js Directory Site with Supabase

You are tasked to create a Next.js directory website using Supabase data and Next.js v0 designs. Your goal is to make the site fully SEO optimized for Google and other major search engines. Follow these instructions and examples to ensure the site is comprehensive, user-friendly, and ranks well.

## Instructions

1. **Directory Structure & Data Integration**
   - Use the provided Supabase business data schema. Each business entry should include all essential fields (name, address, phone, website, description, categories, reviews, images, social links, area served, tags, schema.org data, etc.).
   - Implement dynamic routing using the `slug` field for clean, SEO-friendly URLs.

2. **SEO Content & Review Strategy**
   - On each business page, display only the total number of reviews and the average star rating. Do not display individual review text.
   - Generate a longer, detailed summary snippet that captures the sentiment and keywords from reviews. If there are negative reviews, mention the specific issue in a subtle, concise way (e.g., "One reviewer found the website difficult to use," or "A visitor felt a staff member could have been more welcoming").
     - *Example:*  
       - Review: "Great job fixing our garage door springs."  
       - Snippet: "Customers praise their quick work with broken springs, highlighting prompt service and attention to detail."
       - Negative Review Example: "A reviewer noted the website was not easy to navigate."
     - This summarizes sentiment and keywords, not a direct copy.
   - Beneath the summary, add a clear call-to-action link:
     - "Read all [Number] reviews on Google"
     - "See more reviews on their Google Business Profile"
     - Link directly to the original source.

   **Why this works for SEO:**
   - Provides a trust signal (star rating and review count).
   - Strong user experience (key info and path to verify).
   - Avoids duplicate content issues (no copied review text).

3. **Service Highlights & FAQ Sections**
   - Add a section like "Popular Services and Customer Feedback" or "What Customers Are Saying" using keywords and phrases from reviews to create unique content.
     - *Example:*  
       - Review: "They came out on a Sunday to fix our opener when it wouldn't close. Saved the day!"  
       - Site Content: "Offers emergency garage door repair, including weekend and after-hours service."
   - Add a Frequently Asked Questions section using review-derived questions and problems.
     - *Example:*  
       - Review: "The old spring was so loud. They replaced it with a new, quiet one."  
       - FAQ:  
         - Q: "Do they replace loud or broken garage door springs?"  
         - A: "They specialize in spring replacement, which can fix issues with noisy operation."

4. **Blog & Informational Content**
   - Analyze common keywords from reviews (e.g., "broken cable", "noisy motor", "off-track door", "remote not working").
   - Write blog posts or guides addressing these topics.
     - *Example:*  
       - Blog Post: "5 Common Garage Door Problems and How to Know When to Call a Professional."
   - Internally link blog posts to relevant service pages and businesses.

5. **SEO Technical Best Practices**
   - Use semantic HTML tags (`<header>`, `<main>`, `<footer>`, `<article>`, `<section>`).
   - Optimize page speed (Next.js Image component, caching, minimal scripts).
   - Ensure mobile responsiveness and accessibility (alt text, ARIA, color contrast, keyboard navigation).
   - Set unique and descriptive `<title>` and `<meta description>` for each page.
   - Add Open Graph and Twitter meta tags for social sharing.
   - Implement JSON-LD schema for local business, reviews, and services.
   - Link related pages for better navigation and SEO.
   - Write clear, helpful, and original content for each service/location.
   - Include FAQs, testimonials, and trust signals.
   - Create dedicated pages for each service area.
   - Embed Google Maps and local contact info. Ensure NAP (Name, Address, Phone) consistency.
   - Use Next.js SSR/SSG for fast initial loads. Minimize third-party scripts.
   - Generate and submit XML sitemap. Use robots.txt to control crawling.

6. **Extra Expert SEO Tips**
   - Use internal linking to connect all relevant content and improve crawlability.
   - Regularly update content to keep information fresh and accurate.
   - Encourage businesses to collect more reviews and update their profiles.
   - Monitor site performance and fix any technical SEO issues promptly.
   - Ensure all images are optimized and have descriptive alt text.
   - Use schema.org structured data to enhance search result appearance.

## Example Scenario

- A business page for "AhMa Garage Door Repair" should show:
  - Name, address, phone, website, service categories, opening hours, images, and logo.
  - Star rating and total review count.
  - A summary snippet: "Customers praise their fast emergency repairs and friendly service."
  - "Read all 120 reviews on Google" link.
  - Service highlights: "Emergency repairs available on weekends and after-hours."
  - FAQ:  
    - Q: "Do they fix noisy garage doors?"  
    - A: "Yes, they specialize in quiet spring replacements."
  - Blog post: "5 Common Garage Door Problems" linked to the business and service pages.
  - Embedded Google Map and consistent NAP info.
  - All meta tags, structured data, and technical SEO best practices applied.

**Your output should be a Next.js directory site that is fully SEO ready, easy to navigate, and optimized for Google. Follow all instructions and examples above.**
