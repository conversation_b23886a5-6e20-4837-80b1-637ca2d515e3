# LawConnect - Car Accident Lawyers Directory

A modern, responsive website for finding car accident lawyers and personal injury attorneys. Built with HTML, CSS, and JavaScript, integrated with Supabase for data management.

## Features

### 🏠 Homepage
- **Hero Section**: Professional landing with search functionality
- **Category Browse**: 8 legal service categories with dynamic counts
- **Featured Law Firms**: Top-rated firms with ratings and reviews
- **How We Work**: 3-step process explanation
- **Trending Lawyers**: Popular attorneys carousel

### 🔍 Search & Filter
- **Smart Search**: Search by lawyer name, firm, or specialty
- **Location Filter**: Find lawyers by city or state
- **Category Filter**: Browse by legal specialty
- **Popular Tags**: Quick search for common terms

### ⭐ Review System (Following NextJS SEO Guidelines)
- **Aggregated Summaries**: Shows total reviews and average ratings
- **Smart Snippets**: AI-generated summaries instead of raw review text
- **Read More Links**: Direct links to original Google reviews
- **FAQ Generation**: Automatic FAQ creation from review patterns
- **SEO-Optimized**: Avoids duplicate content issues

### 👨‍💼 Lawyer Profiles
- **Detailed Information**: Contact details, specialties, ratings
- **Review Summaries**: Professional review analysis
- **Service Highlights**: Popular services based on client feedback
- **Office Hours**: Business hours and availability
- **Location Info**: Address and directions

## File Structure

```
html/
├── index.html              # Main homepage
├── lawyer-detail.html      # Individual lawyer pages
├── css/
│   └── styles.css         # Main stylesheet
├── js/
│   ├── main.js           # Main application logic
│   └── supabase-client.js # Supabase integration
└── README.md             # This file
```

## Setup Instructions

### 1. Supabase Configuration
1. Open `html/js/supabase-client.js`
2. Replace the `SUPABASE_ANON_KEY` with your actual Supabase anonymous key
3. Verify the `SUPABASE_URL` matches your project URL

### 2. Data Tables
The website expects two Supabase tables:

#### `findcaraccidentattorneys-clean`
Main lawyer data with fields:
- `source_id` (unique identifier)
- `title` (lawyer/firm name)
- `address`, `city`, `state` (location info)
- `phone`, `website` (contact info)
- `total_score` (rating out of 5)
- `reviews_count` (number of reviews)
- `category_name` (legal specialty)
- `place_id` (Google Places ID)
- `opening_hours` (business hours array)

#### `findcaraccidentattorneys-reviews-all`
Review data with fields:
- `id` (unique identifier)
- `author` (reviewer name)
- `rating` (1-5 stars)
- `text` (review content)
- `review_date` (when review was posted)
- `url` (link to original review)

### 3. Local Development
1. Serve the files using a local web server (required for CORS)
2. Options:
   - Python: `python -m http.server 8000`
   - Node.js: `npx serve .`
   - VS Code: Use Live Server extension

### 4. Deployment
Upload all files to your web hosting service, ensuring:
- All file paths are correct
- Supabase credentials are configured
- HTTPS is enabled for security

## Key Features Implementation

### Review Strategy (NextJS SEO Compliant)
Following the guidelines in `NextJS SEO Reviews.md`:

1. **Snippet Generation**: Creates summaries instead of copying review text
2. **Keyword Extraction**: Identifies positive/negative themes
3. **FAQ Creation**: Generates questions from review patterns
4. **Trust Signals**: Shows ratings and review counts
5. **Read More Links**: Direct links to original sources

### Responsive Design
- Mobile-first approach
- Flexible grid layouts
- Touch-friendly interactions
- Optimized for all screen sizes

### Performance Optimizations
- Lazy loading for images
- Efficient API calls with pagination
- Caching for frequently accessed data
- Minimal external dependencies

## Customization

### Branding
- Update logo and colors in `css/styles.css`
- Modify brand name in HTML files
- Replace contact information in headers/footers

### Content
- Edit category names and icons
- Update "How We Work" process steps
- Customize FAQ templates
- Modify review summary templates

### Styling
- Primary color: `#2563eb` (blue)
- Secondary color: `#f8fafc` (light gray)
- Text colors: `#1e293b` (dark), `#64748b` (medium)
- Fonts: Segoe UI system font stack

## API Integration

### Main Functions
- `getLawyers()` - Fetch all lawyers
- `searchLawyers(query, location)` - Search functionality
- `getFeaturedLawyers()` - Top-rated lawyers
- `getTrendingLawyers()` - Popular lawyers
- `getReviewsForLawyer(placeId)` - Lawyer reviews

### Review Processing
- `generateSummary(reviews)` - Creates review summaries
- `extractKeywords(reviews)` - Finds positive themes
- `generateFAQFromReviews(reviews)` - Creates FAQ content

## Browser Support
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Security Notes
- Never expose Supabase service key in client-side code
- Use Row Level Security (RLS) policies in Supabase
- Validate all user inputs
- Use HTTPS in production

## Troubleshooting

### Common Issues
1. **CORS Errors**: Serve files through a web server, not file://
2. **API Errors**: Check Supabase credentials and table names
3. **Images Not Loading**: Verify image URLs and fallbacks
4. **Search Not Working**: Ensure Supabase tables have correct structure

### Debug Mode
Add `?debug=true` to URL for console logging and error details.

## License
This project is provided as-is for educational and commercial use.

## Support
For issues or questions, refer to the Supabase documentation and browser developer tools for debugging.
