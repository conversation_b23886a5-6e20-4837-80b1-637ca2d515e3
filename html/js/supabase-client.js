// Supabase Configuration
const SUPABASE_URL = 'https://rhcwssrsdwakputbbpuf.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJoY3dzc3JzZHdha3B1dGJicHVmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTY0MzgyMjcsImV4cCI6MjA3MjAxNDIyN30.YwvU9e2RbBWZqXOq4V0sMdwq0Qz-Jl8NOg-MTLNcME0'; // You'll need to replace this with your actual anon key

// Supabase Client Class
class SupabaseClient {
    constructor(url, key) {
        this.url = url;
        this.key = key;
        this.headers = {
            'apikey': key,
            'Authorization': `Bearer ${key}`,
            'Content-Type': 'application/json',
            'Prefer': 'return=representation'
        };
    }

    async request(endpoint, options = {}) {
        const url = `${this.url}/rest/v1/${endpoint}`;
        const config = {
            headers: this.headers,
            ...options
        };

        try {
            const response = await fetch(url, config);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return await response.json();
        } catch (error) {
            console.error('Supabase request error:', error);
            throw error;
        }
    }

    // Get all lawyers
    async getLawyers(limit = 50, offset = 0) {
        return this.request(`findcaraccidentattorneys-clean?limit=${limit}&offset=${offset}&order=total_score.desc`);
    }

    // Get lawyer by ID
    async getLawyerById(id) {
        const result = await this.request(`findcaraccidentattorneys-clean?source_id=eq.${id}`);
        return result[0] || null;
    }

    // Search lawyers
    async searchLawyers(query, location = '', limit = 20) {
        let endpoint = 'findcaraccidentattorneys-clean?';
        
        if (query) {
            endpoint += `title.ilike.*${query}*&`;
        }
        
        if (location) {
            endpoint += `or=(city.ilike.*${location}*,state.ilike.*${location}*)&`;
        }
        
        endpoint += `limit=${limit}&order=total_score.desc`;
        
        return this.request(endpoint);
    }

    // Get lawyers by city
    async getLawyersByCity(city, limit = 20) {
        return this.request(`findcaraccidentattorneys-clean?city=eq.${city}&limit=${limit}&order=total_score.desc`);
    }

    // Get lawyers by state
    async getLawyersByState(state, limit = 20) {
        return this.request(`findcaraccidentattorneys-clean?state=eq.${state}&limit=${limit}&order=total_score.desc`);
    }

    // Get reviews for a lawyer
    async getReviewsForLawyer(placeId, limit = 10) {
        return this.request(`findcaraccidentattorneys-reviews-all?url.cs.*${placeId}*&limit=${limit}&order=review_date.desc`);
    }

    // Get all reviews
    async getAllReviews(limit = 50, offset = 0) {
        return this.request(`findcaraccidentattorneys-reviews-all?limit=${limit}&offset=${offset}&order=review_date.desc`);
    }

    // Get featured lawyers (highest rated)
    async getFeaturedLawyers(limit = 6) {
        return this.request(`findcaraccidentattorneys-clean?total_score=gte.4.5&reviews_count=gte.10&limit=${limit}&order=total_score.desc`);
    }

    // Get trending lawyers (most reviewed recently)
    async getTrendingLawyers(limit = 8) {
        return this.request(`findcaraccidentattorneys-clean?reviews_count=gte.5&limit=${limit}&order=reviews_count.desc`);
    }

    // Get lawyers by category/specialty
    async getLawyersByCategory(category, limit = 20) {
        return this.request(`findcaraccidentattorneys-clean?category_name.ilike.*${category}*&limit=${limit}&order=total_score.desc`);
    }

    // Get statistics
    async getStats() {
        try {
            const lawyers = await this.request('findcaraccidentattorneys-clean?select=source_id');
            const reviews = await this.request('findcaraccidentattorneys-reviews-all?select=id');
            
            return {
                totalLawyers: lawyers.length,
                totalReviews: reviews.length,
                totalFirms: Math.floor(lawyers.length * 0.3) // Estimate
            };
        } catch (error) {
            console.error('Error getting stats:', error);
            return {
                totalLawyers: 150,
                totalReviews: 500,
                totalFirms: 45
            };
        }
    }
}

// Review Summary Generator (following NextJS SEO Reviews guidelines)
class ReviewSummaryGenerator {
    static generateSummary(reviews) {
        if (!reviews || reviews.length === 0) {
            return {
                summary: "No reviews available yet.",
                totalReviews: 0,
                averageRating: 0,
                readMoreLink: null
            };
        }

        const totalReviews = reviews.length;
        const averageRating = reviews.reduce((sum, review) => sum + parseInt(review.rating), 0) / totalReviews;
        
        // Generate summary based on review sentiment and keywords
        const positiveReviews = reviews.filter(r => parseInt(r.rating) >= 4);
        const negativeReviews = reviews.filter(r => parseInt(r.rating) <= 2);
        
        let summary = "";
        
        if (positiveReviews.length > 0) {
            const keywords = this.extractKeywords(positiveReviews);
            summary += `Clients praise their ${keywords.join(', ')}, highlighting professional service and successful case outcomes. `;
        }
        
        if (negativeReviews.length > 0) {
            const negativeKeywords = this.extractNegativeKeywords(negativeReviews);
            if (negativeKeywords.length > 0) {
                summary += `Some reviewers noted concerns about ${negativeKeywords.join(' and ')}.`;
            }
        }
        
        if (summary === "") {
            summary = "Mixed reviews from clients with varying experiences in legal representation.";
        }

        // Get the first review's URL for "Read More" link
        const readMoreLink = reviews[0]?.url || null;

        return {
            summary: summary.trim(),
            totalReviews,
            averageRating: Math.round(averageRating * 10) / 10,
            readMoreLink
        };
    }

    static extractKeywords(reviews) {
        const commonPositiveTerms = [
            'professional service', 'quick response', 'excellent communication',
            'successful outcome', 'knowledgeable team', 'fair settlement',
            'caring approach', 'thorough preparation', 'expert guidance'
        ];
        
        const foundKeywords = [];
        const reviewTexts = reviews.map(r => r.text.toLowerCase()).join(' ');
        
        commonPositiveTerms.forEach(term => {
            if (reviewTexts.includes(term.toLowerCase()) || 
                this.containsSimilarTerms(reviewTexts, term)) {
                foundKeywords.push(term);
            }
        });
        
        return foundKeywords.slice(0, 3); // Return top 3 keywords
    }

    static extractNegativeKeywords(reviews) {
        const commonNegativeTerms = [
            'communication issues', 'delayed response', 'billing concerns',
            'lack of updates', 'unprofessional behavior'
        ];
        
        const foundKeywords = [];
        const reviewTexts = reviews.map(r => r.text.toLowerCase()).join(' ');
        
        commonNegativeTerms.forEach(term => {
            if (reviewTexts.includes(term.toLowerCase()) || 
                this.containsSimilarTerms(reviewTexts, term)) {
                foundKeywords.push(term);
            }
        });
        
        return foundKeywords.slice(0, 2); // Return top 2 negative keywords
    }

    static containsSimilarTerms(text, term) {
        const termWords = term.split(' ');
        return termWords.some(word => text.includes(word));
    }

    static generateFAQFromReviews(reviews) {
        const faqs = [];
        
        // Common questions based on review patterns
        const questionPatterns = [
            {
                keywords: ['fee', 'cost', 'expensive', 'price'],
                question: 'What are their fees and payment structure?',
                answer: 'They work on a contingency fee basis, meaning you only pay if they win your case.'
            },
            {
                keywords: ['communication', 'respond', 'call back', 'update'],
                question: 'How is their communication with clients?',
                answer: 'Clients report regular updates and responsive communication throughout the legal process.'
            },
            {
                keywords: ['settlement', 'win', 'successful', 'result'],
                question: 'What kind of results do they achieve?',
                answer: 'They have a track record of successful settlements and favorable outcomes for car accident cases.'
            }
        ];

        const reviewTexts = reviews.map(r => r.text.toLowerCase()).join(' ');
        
        questionPatterns.forEach(pattern => {
            if (pattern.keywords.some(keyword => reviewTexts.includes(keyword))) {
                faqs.push({
                    question: pattern.question,
                    answer: pattern.answer
                });
            }
        });

        return faqs;
    }
}

// Initialize Supabase client
const supabase = new SupabaseClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Export for use in other files
window.supabase = supabase;
window.ReviewSummaryGenerator = ReviewSummaryGenerator;
