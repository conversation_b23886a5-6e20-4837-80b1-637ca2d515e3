// Main Application JavaScript
class LawConnectApp {
    constructor() {
        this.currentPage = 1;
        this.itemsPerPage = 12;
        this.currentFilters = {
            search: '',
            location: '',
            category: ''
        };
        this.init();
    }

    async init() {
        this.bindEvents();
        await this.loadInitialData();
    }

    bindEvents() {
        // Search functionality
        const searchInput = document.getElementById('searchInput');
        const locationInput = document.getElementById('locationInput');
        const findBtn = document.querySelector('.find-btn');

        if (findBtn) {
            findBtn.addEventListener('click', () => this.handleSearch());
        }

        if (searchInput) {
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') this.handleSearch();
            });
        }

        if (locationInput) {
            locationInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') this.handleSearch();
            });
        }

        // Category cards
        document.querySelectorAll('.category-card').forEach(card => {
            card.addEventListener('click', () => {
                const category = card.querySelector('h3').textContent;
                this.handleCategorySearch(category);
            });
        });

        // Popular search tags
        document.querySelectorAll('.tag').forEach(tag => {
            tag.addEventListener('click', () => {
                const searchTerm = tag.textContent;
                this.handleTagSearch(searchTerm);
            });
        });

        // Carousel controls
        this.bindCarouselEvents();
    }

    bindCarouselEvents() {
        const carouselControls = document.querySelectorAll('.carousel-controls');
        carouselControls.forEach(control => {
            const prevBtn = control.querySelector('.prev');
            const nextBtn = control.querySelector('.next');
            
            if (prevBtn) prevBtn.addEventListener('click', () => this.handleCarouselPrev(control));
            if (nextBtn) nextBtn.addEventListener('click', () => this.handleCarouselNext(control));
        });
    }

    async loadInitialData() {
        try {
            // Load featured law firms
            await this.loadFeaturedFirms();
            
            // Load trending lawyers
            await this.loadTrendingLawyers();
            
            // Update stats
            await this.updateStats();
            
            // Update category counts
            await this.updateCategoryCounts();
            
        } catch (error) {
            console.error('Error loading initial data:', error);
            this.showError('Failed to load data. Please refresh the page.');
        }
    }

    async loadFeaturedFirms() {
        const container = document.getElementById('featuredFirms');
        if (!container) return;

        container.innerHTML = '<div class="loading"><div class="spinner"></div></div>';

        try {
            const firms = await supabase.getFeaturedLawyers(6);
            container.innerHTML = '';

            if (firms.length === 0) {
                container.innerHTML = '<p>No featured firms available at the moment.</p>';
                return;
            }

            firms.forEach(firm => {
                const firmCard = this.createFirmCard(firm);
                container.appendChild(firmCard);
            });

        } catch (error) {
            console.error('Error loading featured firms:', error);
            container.innerHTML = '<p>Error loading featured firms.</p>';
        }
    }

    async loadTrendingLawyers() {
        const container = document.getElementById('trendingLawyers');
        if (!container) return;

        container.innerHTML = '<div class="loading"><div class="spinner"></div></div>';

        try {
            const lawyers = await supabase.getTrendingLawyers(8);
            container.innerHTML = '';

            if (lawyers.length === 0) {
                container.innerHTML = '<p>No trending lawyers available at the moment.</p>';
                return;
            }

            lawyers.forEach(lawyer => {
                const lawyerCard = this.createLawyerCard(lawyer);
                container.appendChild(lawyerCard);
            });

        } catch (error) {
            console.error('Error loading trending lawyers:', error);
            container.innerHTML = '<p>Error loading trending lawyers.</p>';
        }
    }

    createFirmCard(firm) {
        const card = document.createElement('div');
        card.className = 'firm-card';
        
        const rating = firm.total_score || 0;
        const reviewCount = firm.reviews_count || 0;
        const stars = this.generateStars(rating);
        
        card.innerHTML = `
            <div class="firm-image">
                <img src="${firm.image_url || 'https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'}" 
                     alt="${firm.title}" 
                     onerror="this.src='https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'">
                <div class="rating-badge">${rating.toFixed(1)}</div>
            </div>
            <div class="firm-info">
                <div class="firm-rating">
                    <div class="stars">${stars}</div>
                    <span>(${reviewCount})</span>
                </div>
                <h3>${firm.title}</h3>
                <p class="firm-location">${firm.city}, ${firm.state}</p>
                <div class="firm-actions">
                    <button class="book-btn" onclick="window.open('${firm.website || '#'}', '_blank')">
                        Book Now <i class="fas fa-arrow-right"></i>
                    </button>
                    <button class="reviews-btn" onclick="app.showReviews('${firm.place_id}')">
                        <i class="fas fa-star"></i> See Reviews
                    </button>
                </div>
            </div>
        `;
        
        return card;
    }

    createLawyerCard(lawyer) {
        const card = document.createElement('div');
        card.className = 'lawyer-card';
        
        const rating = lawyer.total_score || 0;
        const reviewCount = lawyer.reviews_count || 0;
        const stars = this.generateStars(rating);
        
        card.innerHTML = `
            <div class="lawyer-image">
                <img src="${lawyer.image_url || 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80'}" 
                     alt="${lawyer.title}"
                     onerror="this.src='https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80'">
                <div class="price-badge">$${Math.floor(Math.random() * 200) + 100}</div>
            </div>
            <div class="lawyer-info">
                <div class="lawyer-specialty">${lawyer.category_name || 'Personal Injury Attorney'}</div>
                <div class="lawyer-rating">
                    <div class="stars">${stars}</div>
                    <span>(${reviewCount})</span>
                </div>
                <h3>${lawyer.title}</h3>
                <p class="lawyer-location">${lawyer.city}, ${lawyer.state}</p>
            </div>
        `;
        
        card.addEventListener('click', () => {
            this.showLawyerDetails(lawyer);
        });
        
        return card;
    }

    generateStars(rating) {
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 >= 0.5;
        let stars = '';
        
        for (let i = 0; i < fullStars; i++) {
            stars += '<i class="fas fa-star"></i>';
        }
        
        if (hasHalfStar) {
            stars += '<i class="fas fa-star-half-alt"></i>';
        }
        
        const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
        for (let i = 0; i < emptyStars; i++) {
            stars += '<i class="far fa-star"></i>';
        }
        
        return stars;
    }

    async updateStats() {
        try {
            const stats = await supabase.getStats();
            
            // Update hero stats
            const statElements = document.querySelectorAll('.stat h3');
            if (statElements.length >= 3) {
                statElements[0].textContent = this.formatNumber(stats.totalLawyers * 3.33); // Trusted Users
                statElements[1].textContent = this.formatNumber(stats.totalLawyers); // Verified Lawyers
                statElements[2].textContent = `${stats.totalFirms}+`; // Law Firms
            }
        } catch (error) {
            console.error('Error updating stats:', error);
        }
    }

    async updateCategoryCounts() {
        const categories = [
            { selector: '.category-card:nth-child(1) span', category: 'car accident' },
            { selector: '.category-card:nth-child(2) span', category: 'personal injury' },
            { selector: '.category-card:nth-child(3) span', category: 'insurance' },
            { selector: '.category-card:nth-child(4) span', category: 'motorcycle' },
            { selector: '.category-card:nth-child(5) span', category: 'truck' },
            { selector: '.category-card:nth-child(6) span', category: 'medical malpractice' },
            { selector: '.category-card:nth-child(7) span', category: 'wrongful death' },
            { selector: '.category-card:nth-child(8) span', category: 'law firm' }
        ];

        for (const cat of categories) {
            try {
                const lawyers = await supabase.getLawyersByCategory(cat.category, 100);
                const element = document.querySelector(cat.selector);
                if (element) {
                    element.textContent = `(${lawyers.length})`;
                }
            } catch (error) {
                console.error(`Error updating count for ${cat.category}:`, error);
            }
        }
    }

    formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(0) + 'K';
        }
        return num.toString();
    }

    async handleSearch() {
        const searchInput = document.getElementById('searchInput');
        const locationInput = document.getElementById('locationInput');
        
        const searchTerm = searchInput?.value.trim() || '';
        const location = locationInput?.value.trim() || '';
        
        if (!searchTerm && !location) {
            this.showError('Please enter a search term or location.');
            return;
        }

        try {
            const results = await supabase.searchLawyers(searchTerm, location);
            this.displaySearchResults(results, searchTerm, location);
        } catch (error) {
            console.error('Search error:', error);
            this.showError('Search failed. Please try again.');
        }
    }

    async handleCategorySearch(category) {
        try {
            const results = await supabase.getLawyersByCategory(category);
            this.displaySearchResults(results, category, '');
        } catch (error) {
            console.error('Category search error:', error);
            this.showError('Category search failed. Please try again.');
        }
    }

    async handleTagSearch(tag) {
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.value = tag;
            await this.handleSearch();
        }
    }

    displaySearchResults(results, searchTerm, location) {
        // This would typically navigate to a results page
        // For now, we'll show an alert with the number of results
        alert(`Found ${results.length} lawyers for "${searchTerm}" ${location ? `in ${location}` : ''}`);
        console.log('Search results:', results);
    }

    async showReviews(placeId) {
        try {
            const reviews = await supabase.getReviewsForLawyer(placeId);
            const summary = ReviewSummaryGenerator.generateSummary(reviews);
            
            const modal = this.createReviewModal(summary, reviews);
            document.body.appendChild(modal);
        } catch (error) {
            console.error('Error loading reviews:', error);
            this.showError('Failed to load reviews.');
        }
    }

    createReviewModal(summary, reviews) {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.style.cssText = `
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background: rgba(0,0,0,0.5); display: flex; align-items: center;
            justify-content: center; z-index: 1000;
        `;
        
        const content = document.createElement('div');
        content.className = 'modal-content';
        content.style.cssText = `
            background: white; padding: 30px; border-radius: 15px;
            max-width: 600px; width: 90%; max-height: 80vh; overflow-y: auto;
        `;
        
        content.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h2>Client Reviews</h2>
                <button onclick="this.closest('.modal-overlay').remove()" style="background: none; border: none; font-size: 24px; cursor: pointer;">&times;</button>
            </div>
            <div style="margin-bottom: 20px;">
                <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                    <div class="stars" style="color: #fbbf24;">${this.generateStars(summary.averageRating)}</div>
                    <span>${summary.averageRating}/5 (${summary.totalReviews} reviews)</span>
                </div>
                <p style="color: #64748b; line-height: 1.6;">${summary.summary}</p>
            </div>
            ${summary.readMoreLink ? `
                <a href="${summary.readMoreLink}" target="_blank" style="
                    display: inline-block; background: #2563eb; color: white;
                    padding: 10px 20px; border-radius: 20px; text-decoration: none;
                    font-weight: 500; margin-top: 15px;
                ">Read all reviews on Google <i class="fas fa-external-link-alt"></i></a>
            ` : ''}
        `;
        
        modal.appendChild(content);
        
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });
        
        return modal;
    }

    showLawyerDetails(lawyer) {
        // This would typically navigate to a lawyer detail page
        // For now, we'll show basic info in an alert
        alert(`${lawyer.title}\n${lawyer.city}, ${lawyer.state}\nRating: ${lawyer.total_score}/5\nPhone: ${lawyer.phone || 'Not available'}`);
    }

    handleCarouselPrev(control) {
        const section = control.closest('section');
        const grid = section.querySelector('.featured-grid, .trending-grid');
        if (grid) {
            grid.scrollBy({ left: -300, behavior: 'smooth' });
        }
    }

    handleCarouselNext(control) {
        const section = control.closest('section');
        const grid = section.querySelector('.featured-grid, .trending-grid');
        if (grid) {
            grid.scrollBy({ left: 300, behavior: 'smooth' });
        }
    }

    showError(message) {
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed; top: 20px; right: 20px; background: #ef4444;
            color: white; padding: 15px 20px; border-radius: 10px;
            z-index: 1000; font-weight: 500;
        `;
        errorDiv.textContent = message;
        
        document.body.appendChild(errorDiv);
        
        setTimeout(() => {
            errorDiv.remove();
        }, 5000);
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new LawConnectApp();
});
