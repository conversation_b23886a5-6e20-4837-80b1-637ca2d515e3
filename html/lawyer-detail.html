<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lawyer Details - LawConnect</title>
    <meta name="description" content="Detailed information about car accident lawyers and personal injury attorneys.">
    <link rel="stylesheet" href="css/styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .lawyer-detail {
            padding: 40px 0;
            background: white;
        }
        
        .lawyer-header {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }
        
        .lawyer-photo {
            width: 100%;
            height: 300px;
            border-radius: 15px;
            object-fit: cover;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        
        .lawyer-info h1 {
            font-size: 32px;
            font-weight: bold;
            color: #1e293b;
            margin-bottom: 10px;
        }
        
        .lawyer-specialty {
            color: #2563eb;
            font-weight: 500;
            margin-bottom: 15px;
        }
        
        .lawyer-rating {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .contact-info {
            background: #f8fafc;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .contact-info h3 {
            margin-bottom: 15px;
            color: #1e293b;
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
            color: #64748b;
        }
        
        .contact-item i {
            color: #2563eb;
            width: 20px;
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }
        
        .detail-sections {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 40px;
        }
        
        .section-card {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-bottom: 30px;
        }
        
        .section-card h3 {
            font-size: 20px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 20px;
        }
        
        .review-summary {
            background: #f8fafc;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .faq-item {
            border-bottom: 1px solid #e2e8f0;
            padding: 15px 0;
        }
        
        .faq-item:last-child {
            border-bottom: none;
        }
        
        .faq-question {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 8px;
        }
        
        .faq-answer {
            color: #64748b;
            line-height: 1.6;
        }
        
        .sidebar-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }
        
        .hours-list {
            list-style: none;
        }
        
        .hours-list li {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
            border-bottom: 1px solid #f1f5f9;
        }
        
        .hours-list li:last-child {
            border-bottom: none;
        }
        
        @media (max-width: 768px) {
            .lawyer-header {
                grid-template-columns: 1fr;
                text-align: center;
            }
            
            .detail-sections {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-top">
            <div class="container">
                <div class="header-info">
                    <span><i class="fas fa-clock"></i> Monday - Saturday: 7:00am - 11:00pm / Sunday: 8:30am - 10:30pm</span>
                    <div class="header-contact">
                        <span><i class="fas fa-envelope"></i> <EMAIL></span>
                        <span><i class="fas fa-phone"></i> (*************</span>
                    </div>
                </div>
            </div>
        </div>
        <nav class="navbar">
            <div class="container">
                <div class="nav-brand">
                    <i class="fas fa-balance-scale"></i>
                    <span>LawConnect</span>
                </div>
                <ul class="nav-menu">
                    <li><a href="index.html" class="nav-link">Home</a></li>
                    <li><a href="#" class="nav-link">About</a></li>
                    <li><a href="#" class="nav-link">Pages <i class="fas fa-chevron-down"></i></a></li>
                    <li><a href="#" class="nav-link">Dashboard</a></li>
                    <li><a href="#" class="nav-link">Services <i class="fas fa-chevron-down"></i></a></li>
                    <li><a href="#" class="nav-link">Blog</a></li>
                </ul>
                <div class="nav-actions">
                    <button class="search-btn"><i class="fas fa-search"></i></button>
                    <button class="contact-btn">Contact Us <i class="fas fa-arrow-right"></i></button>
                </div>
            </div>
        </nav>
    </header>

    <!-- Lawyer Detail Section -->
    <section class="lawyer-detail">
        <div class="container">
            <!-- Lawyer Header -->
            <div class="lawyer-header" id="lawyerHeader">
                <!-- Content will be loaded dynamically -->
            </div>

            <!-- Detail Sections -->
            <div class="detail-sections">
                <div class="main-content">
                    <!-- About Section -->
                    <div class="section-card">
                        <h3>About This Attorney</h3>
                        <div id="aboutContent">
                            <!-- Content will be loaded dynamically -->
                        </div>
                    </div>

                    <!-- Reviews Summary -->
                    <div class="section-card">
                        <h3>Client Reviews Summary</h3>
                        <div id="reviewsSummary">
                            <!-- Content will be loaded dynamically -->
                        </div>
                    </div>

                    <!-- Popular Services -->
                    <div class="section-card">
                        <h3>Popular Services and Client Feedback</h3>
                        <div id="servicesContent">
                            <!-- Content will be loaded dynamically -->
                        </div>
                    </div>

                    <!-- FAQ Section -->
                    <div class="section-card">
                        <h3>Frequently Asked Questions</h3>
                        <div id="faqContent">
                            <!-- Content will be loaded dynamically -->
                        </div>
                    </div>
                </div>

                <div class="sidebar">
                    <!-- Contact Card -->
                    <div class="sidebar-card">
                        <h3>Contact Information</h3>
                        <div id="contactCard">
                            <!-- Content will be loaded dynamically -->
                        </div>
                    </div>

                    <!-- Hours Card -->
                    <div class="sidebar-card">
                        <h3>Office Hours</h3>
                        <div id="hoursCard">
                            <!-- Content will be loaded dynamically -->
                        </div>
                    </div>

                    <!-- Location Card -->
                    <div class="sidebar-card">
                        <h3>Location</h3>
                        <div id="locationCard">
                            <!-- Content will be loaded dynamically -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-brand">
                    <div>
                        <i class="fas fa-balance-scale"></i>
                        <span>LawConnect</span>
                    </div>
                    <p>Your trusted directory for finding qualified car accident lawyers and personal injury attorneys.</p>
                </div>
                <div class="footer-links">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="#">About Us</a></li>
                        <li><a href="#">Services</a></li>
                        <li><a href="#">Find Lawyers</a></li>
                        <li><a href="#">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-contact">
                    <h4>Contact Info</h4>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> (*************</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 LawConnect. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="js/supabase-client.js"></script>
    <script>
        // Lawyer Detail Page JavaScript
        class LawyerDetailPage {
            constructor() {
                this.lawyerId = this.getLawyerIdFromUrl();
                this.init();
            }

            getLawyerIdFromUrl() {
                const urlParams = new URLSearchParams(window.location.search);
                return urlParams.get('id');
            }

            async init() {
                if (!this.lawyerId) {
                    this.showError('Lawyer ID not found in URL');
                    return;
                }

                try {
                    await this.loadLawyerData();
                } catch (error) {
                    console.error('Error loading lawyer data:', error);
                    this.showError('Failed to load lawyer information');
                }
            }

            async loadLawyerData() {
                // Load lawyer basic info
                const lawyer = await supabase.getLawyerById(this.lawyerId);
                if (!lawyer) {
                    this.showError('Lawyer not found');
                    return;
                }

                // Load reviews
                const reviews = await supabase.getReviewsForLawyer(lawyer.place_id);
                const reviewSummary = ReviewSummaryGenerator.generateSummary(reviews);
                const faqs = ReviewSummaryGenerator.generateFAQFromReviews(reviews);

                // Render all sections
                this.renderLawyerHeader(lawyer, reviewSummary);
                this.renderAboutSection(lawyer);
                this.renderReviewsSection(reviewSummary);
                this.renderServicesSection(reviews);
                this.renderFAQSection(faqs);
                this.renderContactCard(lawyer);
                this.renderHoursCard(lawyer);
                this.renderLocationCard(lawyer);

                // Update page title
                document.title = `${lawyer.title} - LawConnect`;
            }

            renderLawyerHeader(lawyer, reviewSummary) {
                const container = document.getElementById('lawyerHeader');
                const stars = this.generateStars(reviewSummary.averageRating);
                
                container.innerHTML = `
                    <div>
                        <img src="${lawyer.image_url || 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80'}" 
                             alt="${lawyer.title}" class="lawyer-photo"
                             onerror="this.src='https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80'">
                    </div>
                    <div class="lawyer-info">
                        <h1>${lawyer.title}</h1>
                        <div class="lawyer-specialty">${lawyer.category_name || 'Personal Injury Attorney'}</div>
                        <div class="lawyer-rating">
                            <div class="stars" style="color: #fbbf24;">${stars}</div>
                            <span>${reviewSummary.averageRating}/5 (${reviewSummary.totalReviews} reviews)</span>
                        </div>
                        <div class="contact-info">
                            <h3>Quick Contact</h3>
                            <div class="contact-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>${lawyer.address}</span>
                            </div>
                            <div class="contact-item">
                                <i class="fas fa-phone"></i>
                                <span>${lawyer.phone || 'Phone not available'}</span>
                            </div>
                            <div class="contact-item">
                                <i class="fas fa-globe"></i>
                                <a href="${lawyer.website || '#'}" target="_blank">Visit Website</a>
                            </div>
                        </div>
                        <div class="action-buttons">
                            <button class="btn btn-primary" onclick="window.open('tel:${lawyer.phone}')">
                                <i class="fas fa-phone"></i> Call Now
                            </button>
                            <button class="btn btn-secondary" onclick="window.open('${lawyer.website}', '_blank')">
                                <i class="fas fa-calendar"></i> Book Consultation
                            </button>
                        </div>
                    </div>
                `;
            }

            renderAboutSection(lawyer) {
                const container = document.getElementById('aboutContent');
                container.innerHTML = `
                    <p>${lawyer.title} is a ${lawyer.category_name || 'personal injury attorney'} located in ${lawyer.city}, ${lawyer.state}. 
                    With a ${lawyer.total_score}/5 star rating from ${lawyer.reviews_count} client reviews, they specialize in car accident cases and personal injury law.</p>
                    
                    <h4 style="margin: 20px 0 10px 0; color: #1e293b;">Practice Areas:</h4>
                    <ul style="color: #64748b; line-height: 1.8;">
                        <li>Car Accident Claims</li>
                        <li>Personal Injury Cases</li>
                        <li>Insurance Negotiations</li>
                        <li>Medical Malpractice</li>
                        <li>Wrongful Death Claims</li>
                    </ul>
                `;
            }

            renderReviewsSection(reviewSummary) {
                const container = document.getElementById('reviewsSummary');
                container.innerHTML = `
                    <div class="review-summary">
                        <div style="display: flex; align-items: center; gap: 15px; margin-bottom: 15px;">
                            <div class="stars" style="color: #fbbf24; font-size: 20px;">${this.generateStars(reviewSummary.averageRating)}</div>
                            <div>
                                <div style="font-size: 24px; font-weight: bold; color: #1e293b;">${reviewSummary.averageRating}/5</div>
                                <div style="color: #64748b; font-size: 14px;">${reviewSummary.totalReviews} reviews</div>
                            </div>
                        </div>
                        <p style="color: #64748b; line-height: 1.6; margin-bottom: 15px;">${reviewSummary.summary}</p>
                        ${reviewSummary.readMoreLink ? `
                            <a href="${reviewSummary.readMoreLink}" target="_blank" class="btn btn-secondary">
                                Read all reviews on Google <i class="fas fa-external-link-alt"></i>
                            </a>
                        ` : ''}
                    </div>
                `;
            }

            renderServicesSection(reviews) {
                const container = document.getElementById('servicesContent');
                container.innerHTML = `
                    <p style="color: #64748b; margin-bottom: 20px;">Based on client feedback and case experience:</p>
                    <ul style="color: #64748b; line-height: 1.8;">
                        <li><strong>Emergency Legal Services:</strong> Available for urgent car accident cases, including weekend and after-hours consultations.</li>
                        <li><strong>Insurance Claim Assistance:</strong> Expert negotiation with insurance companies to maximize settlement amounts.</li>
                        <li><strong>Medical Documentation:</strong> Comprehensive gathering and organization of medical records and expert testimonies.</li>
                        <li><strong>Court Representation:</strong> Experienced trial advocacy when settlement negotiations are unsuccessful.</li>
                    </ul>
                `;
            }

            renderFAQSection(faqs) {
                const container = document.getElementById('faqContent');
                
                if (faqs.length === 0) {
                    faqs.push(
                        {
                            question: 'What types of car accident cases do they handle?',
                            answer: 'They handle all types of car accident cases including rear-end collisions, intersection accidents, hit-and-run incidents, and multi-vehicle crashes.'
                        },
                        {
                            question: 'How do they charge for their services?',
                            answer: 'They work on a contingency fee basis, meaning you only pay if they successfully recover compensation for your case.'
                        },
                        {
                            question: 'How long does a car accident case typically take?',
                            answer: 'Case duration varies depending on complexity, but most cases are resolved within 6-18 months through settlement or trial.'
                        }
                    );
                }
                
                const faqHTML = faqs.map(faq => `
                    <div class="faq-item">
                        <div class="faq-question">${faq.question}</div>
                        <div class="faq-answer">${faq.answer}</div>
                    </div>
                `).join('');
                
                container.innerHTML = faqHTML;
            }

            renderContactCard(lawyer) {
                const container = document.getElementById('contactCard');
                container.innerHTML = `
                    <div class="contact-item">
                        <i class="fas fa-phone"></i>
                        <span>${lawyer.phone || 'Phone not available'}</span>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-envelope"></i>
                        <span>Contact via website</span>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>${lawyer.address}</span>
                    </div>
                    <button class="btn btn-primary" style="width: 100%; margin-top: 15px;" onclick="window.open('tel:${lawyer.phone}')">
                        <i class="fas fa-phone"></i> Call Now
                    </button>
                `;
            }

            renderHoursCard(lawyer) {
                const container = document.getElementById('hoursCard');
                const hours = lawyer.opening_hours || [
                    { day: 'Monday', hours: 'Open 24 hours' },
                    { day: 'Tuesday', hours: 'Open 24 hours' },
                    { day: 'Wednesday', hours: 'Open 24 hours' },
                    { day: 'Thursday', hours: 'Open 24 hours' },
                    { day: 'Friday', hours: 'Open 24 hours' },
                    { day: 'Saturday', hours: 'Open 24 hours' },
                    { day: 'Sunday', hours: 'Open 24 hours' }
                ];
                
                const hoursHTML = hours.map(hour => `
                    <li>
                        <span>${hour.day}</span>
                        <span>${hour.hours}</span>
                    </li>
                `).join('');
                
                container.innerHTML = `<ul class="hours-list">${hoursHTML}</ul>`;
            }

            renderLocationCard(lawyer) {
                const container = document.getElementById('locationCard');
                container.innerHTML = `
                    <div class="contact-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>${lawyer.city}, ${lawyer.state}</span>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-building"></i>
                        <span>${lawyer.neighborhood || 'Downtown'}</span>
                    </div>
                    <button class="btn btn-secondary" style="width: 100%; margin-top: 15px;" onclick="window.open('https://maps.google.com/?q=${encodeURIComponent(lawyer.address)}', '_blank')">
                        <i class="fas fa-directions"></i> Get Directions
                    </button>
                `;
            }

            generateStars(rating) {
                const fullStars = Math.floor(rating);
                const hasHalfStar = rating % 1 >= 0.5;
                let stars = '';
                
                for (let i = 0; i < fullStars; i++) {
                    stars += '<i class="fas fa-star"></i>';
                }
                
                if (hasHalfStar) {
                    stars += '<i class="fas fa-star-half-alt"></i>';
                }
                
                const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
                for (let i = 0; i < emptyStars; i++) {
                    stars += '<i class="far fa-star"></i>';
                }
                
                return stars;
            }

            showError(message) {
                const container = document.getElementById('lawyerHeader');
                container.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #ef4444;">
                        <i class="fas fa-exclamation-triangle" style="font-size: 48px; margin-bottom: 20px;"></i>
                        <h2>Error</h2>
                        <p>${message}</p>
                        <a href="index.html" class="btn btn-primary" style="margin-top: 20px;">
                            <i class="fas fa-home"></i> Back to Home
                        </a>
                    </div>
                `;
            }
        }

        // Initialize the lawyer detail page when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            new LawyerDetailPage();
        });
    </script>
</body>
</html>
