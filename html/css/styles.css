/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8fafc;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header-top {
    background: #2563eb;
    color: white;
    padding: 8px 0;
    font-size: 14px;
}

.header-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-contact {
    display: flex;
    gap: 20px;
}

.header-contact span {
    display: flex;
    align-items: center;
    gap: 5px;
}

.navbar {
    background: white;
    padding: 15px 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.navbar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 24px;
    font-weight: bold;
    color: #2563eb;
}

.nav-brand i {
    font-size: 28px;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 30px;
}

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: color 0.3s;
}

.nav-link:hover {
    color: #2563eb;
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.search-btn {
    background: none;
    border: none;
    font-size: 18px;
    color: #666;
    cursor: pointer;
    padding: 8px;
}

.contact-btn {
    background: #2563eb;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 25px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background 0.3s;
}

.contact-btn:hover {
    background: #1d4ed8;
}

/* Hero Section */
.hero {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.hero-text h1 {
    font-size: 48px;
    font-weight: bold;
    color: #1e293b;
    margin-bottom: 20px;
    line-height: 1.2;
}

.hero-text p {
    font-size: 18px;
    color: #64748b;
    margin-bottom: 40px;
}

.hero-stats {
    display: flex;
    gap: 40px;
    margin-bottom: 40px;
}

.stat h3 {
    font-size: 36px;
    font-weight: bold;
    color: #1e293b;
}

.stat p {
    color: #64748b;
    font-size: 14px;
}

.hero-buttons {
    display: flex;
    gap: 20px;
}

.btn {
    padding: 15px 25px;
    border: none;
    border-radius: 25px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s;
    text-decoration: none;
}

.btn-primary {
    background: #2563eb;
    color: white;
}

.btn-primary:hover {
    background: #1d4ed8;
    transform: translateY(-2px);
}

.btn-secondary {
    background: #e2e8f0;
    color: #2563eb;
}

.btn-secondary:hover {
    background: #cbd5e1;
    transform: translateY(-2px);
}

.hero-image {
    text-align: center;
}

.hero-image img {
    width: 100%;
    max-width: 500px;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}

/* Search Section */
.search-section {
    padding: 60px 0;
    background: white;
}

.search-section h2 {
    font-size: 32px;
    font-weight: bold;
    color: #1e293b;
    margin-bottom: 10px;
    text-align: center;
}

.search-section p {
    color: #64748b;
    text-align: center;
    margin-bottom: 40px;
}

.search-form {
    display: flex;
    max-width: 800px;
    margin: 0 auto 30px;
    background: white;
    border-radius: 50px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    overflow: hidden;
}

.search-input, .location-input {
    flex: 1;
    display: flex;
    align-items: center;
    padding: 20px 25px;
    gap: 15px;
}

.search-input i, .location-input i {
    color: #2563eb;
    font-size: 18px;
}

.search-input input, .location-input input {
    border: none;
    outline: none;
    font-size: 16px;
    width: 100%;
}

.find-btn {
    background: #2563eb;
    color: white;
    border: none;
    padding: 20px 30px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background 0.3s;
}

.find-btn:hover {
    background: #1d4ed8;
}

.popular-searches {
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.popular-searches span {
    color: #64748b;
    font-weight: 500;
}

.tag {
    background: #2563eb;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    cursor: pointer;
    transition: background 0.3s;
}

.tag:hover {
    background: #1d4ed8;
}

/* Categories Section */
.categories {
    padding: 80px 0;
    background: #f8fafc;
}

.categories h2 {
    font-size: 32px;
    font-weight: bold;
    color: #1e293b;
    text-align: center;
    margin-bottom: 50px;
}

.category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.category-card {
    background: white;
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    transition: transform 0.3s, box-shadow 0.3s;
    cursor: pointer;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.15);
}

.category-card i {
    font-size: 48px;
    color: #2563eb;
    margin-bottom: 20px;
}

.category-card h3 {
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 10px;
}

.category-card span {
    color: #64748b;
    font-size: 14px;
}

/* Featured Section */
.featured {
    padding: 80px 0;
    background: white;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 50px;
}

.section-header h2 {
    font-size: 32px;
    font-weight: bold;
    color: #1e293b;
}

.carousel-controls {
    display: flex;
    gap: 10px;
}

.carousel-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: none;
    background: #2563eb;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s;
}

.carousel-btn:hover {
    background: #1d4ed8;
}

.featured-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

.firm-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    transition: transform 0.3s, box-shadow 0.3s;
}

.firm-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.15);
}

.firm-image {
    height: 200px;
    background: #e2e8f0;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.firm-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.rating-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background: #2563eb;
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 500;
}

.firm-info {
    padding: 25px;
}

.firm-rating {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.stars {
    color: #fbbf24;
}

.firm-info h3 {
    font-size: 20px;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 5px;
}

.firm-location {
    color: #64748b;
    font-size: 14px;
    margin-bottom: 15px;
}

.firm-actions {
    display: flex;
    gap: 10px;
}

.book-btn {
    background: #2563eb;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.3s;
}

.book-btn:hover {
    background: #1d4ed8;
}

.reviews-btn {
    background: #e2e8f0;
    color: #2563eb;
    border: none;
    padding: 10px 20px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.3s;
}

.reviews-btn:hover {
    background: #cbd5e1;
}

/* How We Work Section */
.how-we-work {
    padding: 80px 0;
    background: #f8fafc;
}

.how-we-work h2 {
    font-size: 32px;
    font-weight: bold;
    color: #1e293b;
    text-align: center;
    margin-bottom: 50px;
}

.steps-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
}

.step {
    text-align: center;
    position: relative;
}

.step-number {
    width: 40px;
    height: 40px;
    background: #2563eb;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin: 0 auto 20px;
    font-size: 18px;
}

.step-image {
    width: 120px;
    height: 120px;
    margin: 0 auto 20px;
    border-radius: 50%;
    overflow: hidden;
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.step-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.step h3 {
    font-size: 20px;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 15px;
}

.step p {
    color: #64748b;
    line-height: 1.6;
}

/* Trending Section */
.trending {
    padding: 80px 0;
    background: white;
}

.trending-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
}

.lawyer-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    transition: transform 0.3s, box-shadow 0.3s;
    position: relative;
}

.lawyer-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.15);
}

.lawyer-image {
    height: 200px;
    background: #e2e8f0;
    position: relative;
    overflow: hidden;
}

.lawyer-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.price-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background: #2563eb;
    color: white;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 14px;
    font-weight: 500;
}

.lawyer-info {
    padding: 20px;
}

.lawyer-specialty {
    color: #2563eb;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 5px;
}

.lawyer-rating {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-bottom: 10px;
}

.lawyer-rating .stars {
    color: #fbbf24;
}

.lawyer-rating span {
    color: #64748b;
    font-size: 14px;
}

.lawyer-info h3 {
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 5px;
}

.lawyer-location {
    color: #64748b;
    font-size: 14px;
}

/* Footer */
.footer {
    background: #1e293b;
    color: white;
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 40px;
    margin-bottom: 40px;
}

.footer-brand {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.footer-brand > div:first-child {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 24px;
    font-weight: bold;
}

.footer-brand i {
    font-size: 28px;
    color: #2563eb;
}

.footer-brand p {
    color: #94a3b8;
    line-height: 1.6;
}

.footer-links h4 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
}

.footer-links ul {
    list-style: none;
}

.footer-links li {
    margin-bottom: 10px;
}

.footer-links a {
    color: #94a3b8;
    text-decoration: none;
    transition: color 0.3s;
}

.footer-links a:hover {
    color: white;
}

.footer-contact h4 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
}

.footer-contact p {
    color: #94a3b8;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.footer-bottom {
    border-top: 1px solid #334155;
    padding-top: 20px;
    text-align: center;
    color: #94a3b8;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-text h1 {
        font-size: 36px;
    }

    .hero-stats {
        justify-content: center;
    }

    .search-form {
        flex-direction: column;
        border-radius: 15px;
    }

    .nav-menu {
        display: none;
    }

    .category-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
}

/* Loading States */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40px;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e2e8f0;
    border-top: 4px solid #2563eb;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
